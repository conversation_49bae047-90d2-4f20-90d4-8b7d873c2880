import { convertToCamelCase, convertToSnakeCase } from "@utils/misc"
import {
  gatewayHttpClient,
  serverGatewayHttpClient,
} from "@infrastructure/providers/httpClient"
import { TAddress } from "types/address.type"
import { TApiResponse } from "types/apiResponse.type"
import { tokenRefreshQueue } from "@infrastructure/auth/tokenRefreshQueue"
import { extractUserClaimsFromToken } from "@utils/jwt"
import { addressSchema } from "@domain/validation/addressSchema"

export interface CreateAddressData {
  markAs: string
  mobileNumber: string
  address: string
  city: string | number
  province: string | number
  country: string | number
  postalCode: string
  addressDetail: string
  notes?: string
  district?: string | number
}

export const addressApi = {
  getAll: async (params?: any): Promise<TApiResponse<TAddress[]>> => {
    const response = await serverGatewayHttpClient.get(
      "/api/v1/member/address/get",
      {
        params: {
          pageSize: 100,
          ...params,
        },
      },
    )
    const apiResponse = convertToCamelCase(response.data) as TApiResponse<
      TAddress[]
    >
    return apiResponse
  },

  getById: async (id: number): Promise<TApiResponse<TAddress>> => {
    const response = await serverGatewayHttpClient.get(
      `/api/v1/member/address/get/${id}`,
    )
    const apiResponse = convertToCamelCase(
      response.data,
    ) as TApiResponse<TAddress>
    return apiResponse
  },

  getByMy: async (params?: any): Promise<TApiResponse<TAddress[]>> => {
    const response = await serverGatewayHttpClient.get(
      "/api/v1/member/address/get/my",
      {
        params: {
          pageSize: 100,
          ...params,
        },
      },
    )
    const apiResponse = convertToCamelCase(response.data) as TApiResponse<
      TAddress[]
    >
    return apiResponse
  },

  delete: async (id: number): Promise<void> => {
    try {
      // Get valid token
      const token = await tokenRefreshQueue.getValidToken()
      if (!token || token === "oauth_token") {
        throw new Error("You must be logged in to delete an address.")
      }

      const response = await serverGatewayHttpClient.delete(
        `/api/v1/member/address/delete/${id}`,
      )

      // Handle 204 No Content response
      if (response.status === 204) {
        return
      }

      const apiResponse = convertToCamelCase(response.data) as TApiResponse<any>

      if (apiResponse.status !== "success") {
        throw new Error(apiResponse.message || "Failed to delete address")
      }
    } catch (error: any) {
      // Extract error message from response
      let message = "Failed to delete address. Please try again."

      if (error?.response?.data) {
        const responseData = error.response.data
        if (responseData.Message) {
          message = responseData.Message
        } else if (responseData.message) {
          message = responseData.message
        } else if (responseData.error) {
          message = responseData.error
        }
      } else if (error?.message) {
        message = error.message
      }

      throw new Error(message)
    }
  },

  deleteById: async (id: number): Promise<void> => {
    return addressApi.delete(id)
  },

  create: async (
    memberId: number,
    formData: FormData,
  ): Promise<{ data: TAddress } | null> => {
    try {
      // Build camelCase payload first, then convert to snake_case to match backend
      const rawCamelData = {
        title: (formData.get("title") ?? "").toString(),
        phoneNumber: `${formData.get("country_code") ?? ""}${formData.get("phone_number") ?? ""}`,
        address:
          formData.get("pinpoint")?.toString() ||
          formData.get("address_detail")?.toString() ||
          "",
        cityId: Number(formData.get("city_id") ?? 0),
        provinceId: Number(formData.get("province_id") ?? 0),
        countryId: Number(formData.get("country_id") ?? 0),
        districtId: Number(formData.get("district_id") ?? 0),
        zipCode: (formData.get("zip_code") ?? "").toString(),
        isPrimary: formData.get("is_primary") === "on",
        addressDetail: (formData.get("address_detail") ?? "").toString(),
        notes: (formData.get("notes") ?? "").toString(),
        // Backend expects region_id as string (see AddressMutateInput.RegionID string)
        regionId: (formData.get("region_id") ?? "").toString(),
      }

      const payload = convertToSnakeCase(rawCamelData)

      const response = await serverGatewayHttpClient.post(
        "/api/v1/member/address/create",
        payload,
        {
          headers: {
            "Content-Type": "application/json",
          },
        },
      )

      const converted = convertToCamelCase(response.data) as {
        data: TAddress
      } | null
      return converted
    } catch (error) {
      let message
      if (error instanceof Error) message = error.message
      else message = String(error)
      if (process.env.NODE_ENV !== "production") {
        // eslint-disable-next-line no-console
        console.error("❌ [addressApi.create] Error", message)
      }
      throw new Error(message)
    }
  },

  // New client-side create method with better interface
  createAddress: async (data: CreateAddressData): Promise<TAddress> => {
    try {
      // Validate data
      const validationResult = addressSchema.safeParse(data)
      if (!validationResult.success) {
        const errors = validationResult.error.flatten().fieldErrors
        const errorMessage = Object.entries(errors)
          .map(([field, messages]) => `${field}: ${messages?.join(", ")}`)
          .join("; ")
        throw new Error(`Validation failed: ${errorMessage}`)
      }

      // Get valid token and user ID
      const token = await tokenRefreshQueue.getValidToken()
      if (!token || token === "oauth_token") {
        throw new Error("You must be logged in to create an address.")
      }

      const claims = extractUserClaimsFromToken(token)
      if (!claims?.userId) {
        throw new Error("Invalid authentication. Please log in again.")
      }

      // Build payload
      const payload = convertToSnakeCase({
        title: data.markAs,
        phoneNumber: data.mobileNumber,
        address: data.address,
        cityId: Number(data.city),
        provinceId: Number(data.province),
        countryId: Number(data.country),
        districtId: data.district ? Number(data.district) : 0,
        zipCode: data.postalCode,
        addressDetail: data.addressDetail,
        notes: data.notes || "",
        isPrimary: false, // Default to false
        regionId: "", // Default empty
      })

      const response = await serverGatewayHttpClient.post(
        "/api/v1/member/address/create",
        payload,
        {
          headers: {
            "Content-Type": "application/json",
          },
        },
      )

      const apiResponse = convertToCamelCase(
        response.data,
      ) as TApiResponse<TAddress>

      if (apiResponse.status !== "success") {
        throw new Error(apiResponse.message || "Failed to create address")
      }

      if (!apiResponse.data) {
        throw new Error("No address data returned from server")
      }

      return apiResponse.data as TAddress
    } catch (error: any) {
      // Extract error message from response
      let message = "Failed to create address. Please try again."

      if (error?.response?.data) {
        const responseData = error.response.data
        if (responseData.Message) {
          message = responseData.Message
        } else if (responseData.message) {
          message = responseData.message
        } else if (responseData.error) {
          message = responseData.error
        }
      } else if (error?.message) {
        message = error.message
      }

      if (process.env.NODE_ENV !== "production") {
        // eslint-disable-next-line no-console
        console.error("❌ [addressApi.createAddress] Error", message)
      }

      throw new Error(message)
    }
  },

  update: async (id: number, data: any): Promise<{ data: TAddress } | null> => {
    try {
      const rawFormData = {
        addressType: data.addressType ?? "",
        recipientName: data.recipientName ?? "",
        phoneNumber: data.phoneNumber ?? "",
        address: data.address ?? "",
        cityId: Number(data.cityId) ?? 0,
        provinceId: Number(data.provinceId) ?? 0,
        countryId: Number(data.countryId) ?? 0,
        postalCode: data.postalCode ?? "",
        isDefault: data.isDefault ?? false,
        isActive: data.isActive ?? true,
      }
      const payload = convertToSnakeCase(rawFormData)
      const formData = new FormData()
      formData.append("payload", JSON.stringify(payload))
      const response = await gatewayHttpClient.put(
        `/api/v1/member/address/update/${data.id}`,
        formData,
        {
          headers: {
            contentType: "multipart/form-data",
          },
        },
      )
      return convertToCamelCase(response.data) as { data: TAddress } | null
    } catch (error) {
      let message
      if (error instanceof Error) message = error.message
      else message = String(error)
      throw new Error(message)
    }
  },

  updatePrimary: async (
    data: TAddress,
    isPrimary: boolean,
  ): Promise<TApiResponse<TAddress>> => {
    const response = await gatewayHttpClient.put(
      `member/address/update/${data.id}`,
      JSON.stringify(
        convertToSnakeCase({
          ...data,
          isPrimary,
        } as TAddress),
      ),
      {
        validateStatus: (status) => {
          return status < 500
        },
      },
    )
    const apiResponse = convertToCamelCase(
      response.data,
    ) as TApiResponse<TAddress>
    return apiResponse
  },
}
