// eslint-disable max-lines-per-function

import { CreateAddressData } from "@infrastructure/api/addressApi"

/**
 * Convert FormData to CreateAddressData interface
 * Used for converting form submissions to API-compatible format
 */
export const convertFormDataToAddressData = (
  formData: FormData,
): CreateAddressData => {
  // Get form values
  const title = formData.get("title")?.toString() || ""
  const countryCode = formData.get("country_code")?.toString() || ""
  const phoneNumber = formData.get("phone_number")?.toString() || ""
  const pinpoint = formData.get("pinpoint")?.toString() || ""
  const addressDetail = formData.get("address_detail")?.toString() || ""
  const cityId = formData.get("city_id")?.toString() || ""
  const provinceId = formData.get("province_id")?.toString() || ""
  const countryId = formData.get("country_id")?.toString() || ""
  const districtId = formData.get("district_id")?.toString() || ""
  const zipCode = formData.get("zip_code")?.toString() || ""
  const notes = formData.get("notes")?.toString() || ""

  return {
    markAs: title,
    mobileNumber: `${countryCode}${phoneNumber}`,
    address: pinpoint || addressDetail, // Use pinpoint if available, fallback to addressDetail
    city: cityId,
    province: provinceId,
    country: countryId,
    district: districtId || undefined,
    postalCode: zipCode,
    addressDetail: addressDetail,
    notes: notes || undefined,
  }
}

/**
 * Convert object data to CreateAddressData interface
 * Used for converting component state or props to API-compatible format
 */
export const convertObjectToAddressData = (data: any): CreateAddressData => {
  return {
    markAs: data.markAs || data.title || "",
    mobileNumber:
      data.mobileNumber || `${data.countryCode || ""}${data.phoneNumber || ""}`,
    address: data.address || data.pinpoint || data.addressDetail || "",
    city: data.city || data.cityId || "",
    province: data.province || data.provinceId || "",
    country: data.country || data.countryId || "",
    district: data.district || data.districtId || undefined,
    postalCode: data.postalCode || data.zipCode || "",
    addressDetail: data.addressDetail || "",
    notes: data.notes || undefined,
  }
}

/**
 * Validate address data before submission
 * Returns validation errors if any
 */
export const validateAddressData = (
  data: CreateAddressData,
): Record<string, string[]> | null => {
  const errors: Record<string, string[]> = {}

  // Required field validations
  if (!data.markAs?.trim()) {
    errors.markAs = ["Address title is required"]
  }

  if (!data.mobileNumber?.trim()) {
    errors.mobileNumber = ["Mobile number is required"]
  } else if (data.mobileNumber.length < 8) {
    errors.mobileNumber = ["Mobile number must be at least 8 digits"]
  }

  if (!data.address?.trim()) {
    errors.address = ["Address is required"]
  }

  if (!data.city) {
    errors.city = ["City is required"]
  }

  if (!data.province) {
    errors.province = ["Province is required"]
  }

  if (!data.country) {
    errors.country = ["Country is required"]
  }

  if (!data.postalCode?.trim()) {
    errors.postalCode = ["Postal code is required"]
  }

  if (!data.addressDetail?.trim()) {
    errors.addressDetail = ["Address detail is required"]
  }

  // Return null if no errors, otherwise return errors object
  return Object.keys(errors).length > 0 ? errors : null
}

/**
 * Format mobile number for display
 * Splits mobile number into country code and phone number
 */
export const formatMobileNumber = (
  mobileNumber: string,
): { countryCode: string; phoneNumber: string } => {
  if (!mobileNumber) {
    return { countryCode: "", phoneNumber: "" }
  }

  // Simple logic: assume first 2-4 digits are country code
  if (mobileNumber.startsWith("+")) {
    // International format: +62812345678
    const countryCode = mobileNumber.slice(0, 3) // +62
    const phoneNumber = mobileNumber.slice(3) // 812345678
    return { countryCode, phoneNumber }
  } else if (mobileNumber.length > 10) {
    // Assume first 2-3 digits are country code: 62812345678
    const countryCode = mobileNumber.slice(0, 2) // 62
    const phoneNumber = mobileNumber.slice(2) // 812345678
    return { countryCode, phoneNumber }
  } else {
    // Local format: 0812345678
    return { countryCode: "", phoneNumber: mobileNumber }
  }
}

/**
 * Convert address data back to form-compatible format
 * Used for populating edit forms
 */
export const convertAddressDataToFormData = (
  data: CreateAddressData,
): Record<string, string> => {
  const { countryCode, phoneNumber } = formatMobileNumber(data.mobileNumber)

  return {
    title: data.markAs,
    country_code: countryCode,
    phone_number: phoneNumber,
    pinpoint: data.address,
    address_detail: data.addressDetail,
    city_id: String(data.city),
    province_id: String(data.province),
    country_id: String(data.country),
    district_id: data.district ? String(data.district) : "",
    zip_code: data.postalCode,
    notes: data.notes || "",
  }
}
